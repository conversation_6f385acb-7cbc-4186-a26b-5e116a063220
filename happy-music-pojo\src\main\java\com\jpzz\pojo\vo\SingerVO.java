package com.jpzz.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 歌手视图对象
 * <AUTHOR>
 */
@Data
@Schema(description = "歌手信息视图对象")
public class SingerVO {

    @Schema(description = "歌手ID", example = "1")
    private Long singerId;

    @Schema(description = "歌手名称", example = "周杰伦")
    private String singerName;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "歌手类型", example = "男歌手")
    private String type;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "出生日期", example = "1979-01-18")
    private LocalDate birth;

    @Schema(description = "地区", example = "中国台湾")
    private String location;

    @Schema(description = "个人简介", example = "华语流行音乐歌手、词曲创作人")
    private String introduction;

    @Schema(description = "歌曲数量", example = "150")
    private Integer songsCount;

    @Schema(description = "粉丝数量", example = "50000000")
    private Long fansCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间", example = "2024-01-01 10:00:00")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间", example = "2024-01-01 10:00:00")
    private LocalDateTime updateTime;
}
package com.jpzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jpzz.pojo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {


    @Select("select * from tb_user where username = #{username}")
    User selectByUserName( String username);
}

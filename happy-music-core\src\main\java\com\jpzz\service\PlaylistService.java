package com.jpzz.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jpzz.pojo.dto.PlaylistDTO;
import com.jpzz.pojo.dto.PlaylistQueryDTO;
import com.jpzz.pojo.dto.PlaylistSongDTO;
import com.jpzz.pojo.dto.PlaylistSongQueryDTO;
import com.jpzz.pojo.entity.Playlist;
import com.jpzz.pojo.vo.PlaylistVO;
import com.jpzz.pojo.vo.SongVO;
import com.jpzz.result.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 歌单服务接口
 *
 * <AUTHOR>
 */
public interface PlaylistService extends IService<Playlist> {

    /**
     * 分页查询歌单列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    PageResult<PlaylistVO> getPlaylistPage(PlaylistQueryDTO queryDTO);

    /**
     * 根据ID查询歌单详情
     *
     * @param playlistId 歌单ID
     * @return 歌单详情
     */
    PlaylistVO getPlaylistById(Long playlistId);

    /**
     * 添加歌单
     *
     * @param playlistDTO 歌单信息
     * @return 歌单ID
     */
    Long addPlaylist(PlaylistDTO playlistDTO);

    /**
     * 更新歌单
     *
     * @param playlistDTO 歌单信息
     */
    void updatePlaylist(PlaylistDTO playlistDTO);

    /**
     * 删除歌单
     *
     * @param playlistId 歌单ID
     */
    void deletePlaylistById(Long playlistId);

    /**
     * 上传歌单封面
     *
     * @param file       封面文件
     * @param playlistId 歌单ID
     * @return 封面URL
     */
    String uploadCover(MultipartFile file, Long playlistId);

    /**
     * 添加歌曲到歌单
     *
     * @param playlistId 歌单ID
     * @param songIds    歌曲ID列表
     */
    void addSongsToPlaylist(Long playlistId, List<Long> songIds);

    /**
     * 从歌单中移除歌曲
     *
     * @param playlistSongDTO 歌单歌曲信息
     */
    void removeSongsByPlaylist(PlaylistSongDTO playlistSongDTO);

    /**
     * 批量删除歌单
     *
     * @param playlistIds 歌单ID列表
     */
    void deletePlaylistByIds(List<Long> playlistIds);

    /**
     * 获取歌单中的歌曲列表
     *
     * @param queryDTO 查询条件
     * @return 歌曲列表
     */
    PageResult<SongVO> getPlaylistSongs(PlaylistSongQueryDTO queryDTO);
}
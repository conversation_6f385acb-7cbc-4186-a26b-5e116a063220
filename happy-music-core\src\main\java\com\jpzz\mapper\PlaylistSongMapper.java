package com.jpzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.pojo.dto.PlaylistSongQueryDTO;
import com.jpzz.pojo.entity.PlaylistSong;
import com.jpzz.pojo.vo.SongVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 歌单歌曲关联Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface PlaylistSongMapper extends BaseMapper<PlaylistSong> {

    /**
     * 分页查询歌单中的歌曲列表
     * @param page 分页对象
     * @param queryDTO 查询条件
     * @return 歌曲列表
     */
    Page<SongVO> selectPlaylistSongs(Page<SongVO> page, @Param("query") PlaylistSongQueryDTO queryDTO);

}

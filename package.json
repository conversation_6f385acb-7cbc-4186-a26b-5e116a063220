{"name": "happy-music-client", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@iconify/json": "^2.2.363", "@iconify/vue": "^5.0.0", "@vueuse/motion": "^3.0.3", "axios": "^1.10.0", "element-plus": "^2.10.3", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@rushstack/eslint-patch": "^1.10.4", "@tsconfig/node20": "^20.1.4", "@types/node": "^22.10.1", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-vue": "^9.31.0", "npm-run-all2": "^7.0.1", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "~5.6.3", "unplugin-auto-import": "^0.18.6", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.27.4", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}
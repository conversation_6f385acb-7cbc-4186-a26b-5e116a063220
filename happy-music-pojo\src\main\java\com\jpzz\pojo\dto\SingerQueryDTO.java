package com.jpzz.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 歌手查询参数DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "歌手查询参数")
public class SingerQueryDTO extends PageQueryDTO {


    @Schema(description = "歌手名称(模糊查询)", example = "周杰伦")
    private String singerName;

    @Schema(description = "歌手类型", example = "男歌手")
    private String type;

    @Schema(description = "地区", example = "中国台湾")
    private String location;

    @Schema(description = "开始时间", example = "2023-01-01")
    private String beginTime;

    @Schema(description = "结束时间", example = "2023-12-31")
    private String endTime;
}
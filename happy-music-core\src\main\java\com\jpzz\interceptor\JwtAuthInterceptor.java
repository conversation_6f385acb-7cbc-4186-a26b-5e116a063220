package com.jpzz.interceptor;

import cn.hutool.core.util.ObjectUtil;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.jpzz.constant.CacheConstant;
import com.jpzz.constant.ResultCodeConstant;
import com.jpzz.service.AuthService;
import com.jpzz.service.CacheService;
import com.jpzz.utils.JwtUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class JwtAuthInterceptor implements HandlerInterceptor {

    @Resource
    private JwtUtils jwtUtils;
    @Resource
    private CacheManager cacheManager;
    @Resource
    private CacheService cacheService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 判断当前拦截到的是Controller的方法还是其他资源
        if (!(handler instanceof HandlerMethod)) {
            // 当前拦截到的不是动态方法（如静态资源），直接放行
            return true;
        }
        log.info("开始拦截");
        // 如果是OPTIONS请求，放行
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }
        String authHeader = request.getHeader("Authorization");
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            response.setStatus(ResultCodeConstant.UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":" + ResultCodeConstant.UNAUTHORIZED + ",\"msg\":\"未登录或登录已过期\"}");
            return false;
        }
        String token = authHeader.substring(7);

        try {
            if (!jwtUtils.validateToken(token)) {
                response.setStatus(ResultCodeConstant.UNAUTHORIZED);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"code\":" + ResultCodeConstant.UNAUTHORIZED + ",\"msg\":\"登录已过期\"}");
                return false;
            }
            // 验证Token是否在缓存中（检查是否已退出登录）
            Long userId = jwtUtils.getUserIdByToken(token);
            String cacheToken = cacheService.getUserToken(userId);
            if (!ObjectUtil.equal(cacheToken, token) || !ObjectUtil.isNotNull(cacheToken)) {
                return false;
            }

            request.setAttribute("userId", jwtUtils.getUserIdByToken(token));
            request.setAttribute("username", jwtUtils.getUserNameByToken(token));
            request.setAttribute("role", jwtUtils.getRoleByToken(token));
            return true;
        } catch (JWTVerificationException e) {
            response.setStatus(ResultCodeConstant.UNAUTHORIZED);
            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write("{\"code\":" + ResultCodeConstant.UNAUTHORIZED + ",\"msg\":\"无效的令牌\"}");
            return false;
        }
    }

    /**
     * 检查Token是否在缓存中
     * @param userId 用户ID
     * @param token 令牌
     * @return 是否在缓存中
     */
   /* private Boolean isTokenCached(Long userId, String token) {
        try {
            Cache cache = cacheManager.getCache(CacheConstant.JWT_TOKEN_CACHE);
            if (ObjectUtil.isNotNull(cache)) {
                Cache.ValueWrapper wrapper = cache.get(userId);
                if (ObjectUtil.isNotNull(wrapper)) {
                    String cachedToken = (String) wrapper.get();
                    return cachedToken.equals(token);
                }
            }
            return false;
        } catch (Exception e) {
            log.error("检查Token缓存失败，用户ID: {}, 错误: {}", userId, e.getMessage());
            return false;
        }
    }*/
}

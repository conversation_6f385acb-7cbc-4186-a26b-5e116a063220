package com.jpzz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jpzz.pojo.dto.FeedbackQueryDTO;
import com.jpzz.pojo.dto.FeedbackUpdateDTO;
import com.jpzz.pojo.entity.Feedback;
import com.jpzz.pojo.vo.FeedbackVO;
import com.jpzz.result.PageResult;

import java.util.List;

/**
 * 反馈服务接口
 * <AUTHOR>
 */
public interface FeedbackService extends IService<Feedback> {

    /**
     * 分页查询反馈列表
     * @param queryDTO 查询参数
     * @return 分页结果
     */
    PageResult<FeedbackVO> pageFeedbackList(FeedbackQueryDTO queryDTO);

    /**
     * 根据ID查询反馈详情
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    FeedbackVO getFeedbackById(Long feedbackId);



    /**
     * 管理员更新反馈（状态/回复）
     * @param feedbackId 反馈ID
     * @param updateDTO 更新信息
     */
    void updateFeedback(Long feedbackId, FeedbackUpdateDTO updateDTO);

    /**
     * 删除反馈
     * @param feedbackId 反馈ID
     */
    void deleteById(Long feedbackId);

    /**
     * 批量删除反馈
     * @param feedbackIds 反馈ID列表
     */
    void deleteByIds(List<Long> feedbackIds);


}

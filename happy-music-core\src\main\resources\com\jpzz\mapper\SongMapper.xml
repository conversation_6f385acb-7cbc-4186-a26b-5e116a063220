<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jpzz.mapper.SongMapper">

    <!-- 歌曲VO结果映射 -->
    <resultMap id="SongVOMap" type="com.jpzz.pojo.vo.SongVO">
        <id column="id" property="id"/>
        <result column="artist_id" property="artistId"/>
        <result column="artist_name" property="artistName"/>
        <result column="name" property="name"/>
        <result column="album" property="album"/>
        <result column="lyric" property="lyric"/>
        <result column="duration" property="duration"/>
        <result column="style" property="style"/>
        <result column="cover_url" property="coverUrl"/>
        <result column="audio_url" property="audioUrl"/>
        <result column="release_time" property="releaseTime"/>
        <result column="like_count" property="likeCount"/>
    </resultMap>

    <!-- 分页查询歌曲列表 -->
    <select id="selectSongPage" resultMap="SongVOMap">
        SELECT
            s.id,
            s.artist_id,
            singer.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count
        FROM tb_song s
        LEFT JOIN tb_singer singer ON s.artist_id = singer.singer_id
        <where>
            AND s.deleted = 0
            <!-- 如果没有指定歌手ID且没有其他搜索条件，则不返回任何结果 -->
            <if test="query.artistId == null and (query.name == null or query.name == '') and (query.artistName == null or query.artistName == '') and (query.style == null or query.style == '') and (query.album == null or query.album == '')">
                AND 1 = 0
            </if>
            <if test="query.name != null and query.name != ''">
                AND s.name LIKE CONCAT('%', #{query.name}, '%')
            </if>
            <if test="query.artistId != null">
                AND s.artist_id = #{query.artistId}
            </if>
            <if test="query.artistName != null and query.artistName != ''">
                AND singer.singer_name LIKE CONCAT('%', #{query.artistName}, '%')
            </if>
            <if test="query.style != null and query.style != ''">
                AND s.style = #{query.style}
            </if>
            <if test="query.album != null and query.album != ''">
                AND s.album LIKE CONCAT('%', #{query.album}, '%')
            </if>
        </where>
        ORDER BY s.like_count DESC, s.id DESC
    </select>

    <!-- 根据ID查询歌曲详情 -->
    <select id="selectSongById" resultMap="SongVOMap">
        SELECT
            s.id,
            s.artist_id,
            singer.singer_name as artist_name,
            s.name,
            s.album,
            s.lyric,
            s.duration,
            s.style,
            s.cover_url,
            s.audio_url,
            s.release_time,
            s.like_count
        FROM tb_song s
        LEFT JOIN tb_singer singer ON s.artist_id = singer.singer_id
        WHERE s.id = #{songId} And s.deleted = 0
    </select>

</mapper>
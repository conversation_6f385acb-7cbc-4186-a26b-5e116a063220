package com.jpzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.pojo.dto.FeedbackQueryDTO;
import com.jpzz.pojo.entity.Feedback;
import com.jpzz.pojo.vo.FeedbackVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 反馈Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface FeedbackMapper extends BaseMapper<Feedback> {

    /**
     * 分页查询反馈列表（关联用户表获取用户信息）
     * @param page 分页对象
     * @param queryDTO 查询条件
     * @return 反馈列表
     */
    Page<FeedbackVO> selectFeedbackPage(Page<FeedbackVO> page, @Param("query") FeedbackQueryDTO queryDTO);

    /**
     * 根据ID查询反馈详情（关联用户表）
     * @param feedbackId 反馈ID
     * @return 反馈详情
     */
    FeedbackVO selectFeedbackById(@Param("feedbackId") Long feedbackId);
}
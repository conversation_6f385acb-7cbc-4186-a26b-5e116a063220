package com.jpzz.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 歌曲视图对象
 * <AUTHOR>
 */
@Data
@Schema(description = "歌曲信息视图对象")
public class SongVO {

    @Schema(description = "歌曲ID", example = "1")
    private Long id;

    @Schema(description = "歌手ID", example = "1")
    private Long artistId;

    @Schema(description = "歌手名称", example = "周杰伦")
    private String artistName;

    @Schema(description = "歌曲名称", example = "青花瓷")
    private String name;

    @Schema(description = "专辑名称", example = "我很忙")
    private String album;

    @Schema(description = "歌词")
    private String lyric;

    @Schema(description = "时长", example = "4:20")
    private String duration;

    @Schema(description = "风格", example = "流行")
    private String style;

    @Schema(description = "封面URL", example = "https://example.com/cover.jpg")
    private String coverUrl;

    @Schema(description = "音频URL", example = "https://example.com/audio.mp3")
    private String audioUrl;

    @Schema(description = "发行时间", example = "2007-11-01")
    private String releaseTime;

    @Schema(description = "点赞数", example = "1000")
    private Integer likeCount;

    @Schema(description = "在歌单中的排序号", example = "1")
    private Integer sortOrder;
}

package com.jpzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.pojo.dto.SongQueryDTO;
import com.jpzz.pojo.entity.Song;
import com.jpzz.pojo.vo.SongVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 歌曲Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface SongMapper extends BaseMapper<Song> {

    /**
     * 分页查询歌曲列表（关联歌手表获取歌手名称）
     * @param page 分页对象
     * @param queryDTO 查询条件
     * @return 歌曲列表
     */
    Page<SongVO> selectSongPage(Page<SongVO> page, @Param("query") SongQueryDTO queryDTO);

    /**
     * 根据ID查询歌曲详情（关联歌手表）
     * @param songId 歌曲ID
     * @return 歌曲详情
     */
    SongVO selectSongById(@Param("songId") Long songId);
}
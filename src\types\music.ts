// 歌曲信息类型
export interface SongInfo {
  id: number
  name: string
  artist: string
  artistId: number
  album?: string
  albumId?: number
  duration: number // 歌曲时长（秒）
  cover?: string // 封面图片URL
  url: string // 音频文件URL
  lyric?: string // 歌词
  playCount: number // 播放次数
  likeCount: number // 点赞数
  status: 0 | 1 // 0: 禁用, 1: 启用
  createTime: string
  updateTime: string
}

// 歌手信息类型
export interface ArtistInfo {
  id: number
  name: string
  avatar?: string
  description?: string
  songCount: number // 歌曲数量
  albumCount: number // 专辑数量
  fanCount: number // 粉丝数量
  status: 0 | 1
  createTime: string
  updateTime: string
}

// 专辑信息类型
export interface AlbumInfo {
  id: number
  name: string
  artistId: number
  artistName: string
  cover?: string
  description?: string
  songCount: number
  releaseDate?: string
  status: 0 | 1
  createTime: string
  updateTime: string
}

// 歌单信息类型
export interface PlaylistInfo {
  id: number
  name: string
  description?: string
  cover?: string
  userId: number
  username: string
  songCount: number
  playCount: number
  isPublic: 0 | 1 // 0: 私有, 1: 公开
  status: 0 | 1
  createTime: string
  updateTime: string
  songs?: SongInfo[] // 歌单中的歌曲列表
}

// 播放器状态类型
export interface PlayerState {
  currentSong: SongInfo | null
  playlist: SongInfo[]
  currentIndex: number
  isPlaying: boolean
  volume: number
  currentTime: number
  duration: number
  playMode: 'order' | 'random' | 'repeat' | 'single' // 播放模式
}

// 音乐查询参数
export interface MusicQueryParams {
  page: number
  size: number
  keyword?: string
  artistId?: number
  albumId?: number
  genre?: string
  sortBy?: 'name' | 'playCount' | 'createTime'
  sortOrder?: 'asc' | 'desc'
}

# Happy Music 项目开发规划

## 项目概述
Happy Music Server 是 HappyMusic 项目的后端 API 服务。本项目基于 Spring Boot 3 构建，采用 Java 17、Maven、MyBatis-Plus、MySQL、Redis 和 MinIO 等技术。

## 技术栈
- 后端框架: Spring Boot 3
- 开发语言: Java 17
- 构建工具: Maven
- 数据库: MySQL (推荐 8.0+)
- ORM: MyBatis-Plus
- 缓存: Redis
- 对象存储: MinIO
- 认证: JWT (java-jwt)
- 数据库连接池: Druid
- 工具库: Lombok, Spring Boot Validation, Java Mail

## 开发进度

### 已完成
- [x] 项目基础架构搭建
- [x] 前端歌手管理页面
- [x] 前端歌曲管理页面
- [x] 基础配置(JWT、MinIO、跨域等)

### 当前任务：后端API开发

#### 1. 歌手管理模块
- [ ] Singer实体类设计
- [ ] SingerController接口开发
- [ ] SingerService业务逻辑
- [ ] SingerMapper数据访问层
- [ ] 歌手头像上传功能

#### 2. 歌曲管理模块  
- [ ] Song实体类设计
- [ ] SongController接口开发
- [ ] SongService业务逻辑
- [ ] SongMapper数据访问层
- [ ] 歌曲封面/音频上传功能

#### 3. 数据库设计
- [ ] 歌手表(t_singer)设计
- [ ] 歌曲表(t_song)设计
- [ ] 建立表关联关系

## 接口设计规范

### 歌手管理接口
```
GET    /singer/list     - 分页查询歌手列表
GET    /singer/{id}     - 获取歌手详情
POST   /singer          - 新增歌手
PUT    /singer          - 更新歌手
DELETE /singer/{id}     - 删除歌手
DELETE /singer/batch    - 批量删除歌手
POST   /singer/{id}/avatar - 上传歌手头像
```

### 歌曲管理接口
```
GET    /song/list       - 分页查询歌曲列表
GET    /song/{id}       - 获取歌曲详情
POST   /song            - 新增歌曲
PUT    /song            - 更新歌曲
DELETE /song/{id}       - 删除歌曲
DELETE /song/batch      - 批量删除歌曲
POST   /song/{id}/cover - 上传歌曲封面
POST   /song/{id}/audio - 上传歌曲音频
```

## 下一步计划
1. 设计数据库表结构
2. 创建实体类和DTO
3. 开发歌手管理相关接口
4. 开发歌曲管理相关接口
5. 实现文件上传功能
6. 前后端联调测试


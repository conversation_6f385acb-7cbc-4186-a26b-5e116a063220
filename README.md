# 🎵 Happy Music 音乐管理系统

## 📖 项目概述

Happy Music 是一个现代化的前后端分离音乐管理系统，为音乐平台提供完整的后台管理解决方案。系统采用最新的技术栈构建，具有高性能、高可用、易扩展的特点。

## 🏗️ 系统架构

### 前端技术栈
- **框架**: Vue 3 + TypeScript + Vite
- **UI库**: Element Plus + Tailwind CSS
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **图表**: ECharts + Vue-ECharts
- **图标**: @iconify/vue
- **动画**: @vueuse/motion

### 后端技术栈
- **框架**: Spring Boot 3
- **语言**: Java 17
- **构建工具**: Maven
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis-Plus
- **缓存**: Redis
- **对象存储**: MinIO
- **认证**: JWT
- **连接池**: Druid
- **API文档**: Knife4j
- **工具库**: Lombok, Hutool

## 📁 项目结构

```
happy-music-admin/
├── src/                          # 前端源码
│   ├── api/                     # API接口层
│   ├── components/              # 公共组件
│   ├── layout/                  # 布局组件
│   ├── router/                  # 路由配置
│   ├── stores/                  # 状态管理
│   ├── types/                   # TypeScript类型
│   ├── utils/                   # 工具函数
│   ├── views/                   # 页面组件
│   └── style/                   # 样式文件
├── happy-music-common/          # 后端公共模块
├── happy-music-pojo/            # 后端实体类模块
├── happy-music-core/            # 后端核心业务模块
└── 项目架构分析报告.md          # 详细架构分析
```

## ✨ 核心功能

### 🎯 已完成功能
- ✅ **用户管理**: 用户CRUD、角色权限、状态管理
- ✅ **歌手管理**: 歌手信息管理、头像上传、统计功能
- ✅ **歌曲管理**: 歌曲信息管理、封面/音频上传、关联查询
- ✅ **歌单管理**: 歌单CRUD、封面上传、歌曲关联
- ✅ **反馈管理**: 用户反馈处理、状态跟踪
- ✅ **仪表板**: 数据统计、图表展示、快捷操作
- ✅ **文件上传**: MinIO对象存储、多类型文件支持
- ✅ **用户认证**: JWT Token、权限控制、登录状态管理

### 🚧 开发中功能
- 🔄 **数据导出**: Excel导出功能
- 🔄 **高级搜索**: 多条件组合搜索
- 🔄 **批量操作**: 批量删除、批量更新
- 🔄 **操作日志**: 用户操作记录和审计

## 🚀 快速开始

### 环境要求
- **前端**: Node.js 18+ 
- **后端**: Java 17+, Maven 3.6+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **存储**: MinIO

### 前端启动
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

### 后端启动
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者运行jar包
java -jar happy-music-core/target/happy-music-core-0.0.1-SNAPSHOT.jar
```

### 数据库配置
```sql
-- 创建数据库
CREATE DATABASE db_music CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入表结构和初始数据
-- (具体SQL文件请联系开发团队获取)
```

## 📚 API文档

启动后端服务后，访问以下地址查看API文档：
- **Knife4j文档**: http://localhost:8080/doc.html
- **Swagger UI**: http://localhost:8080/swagger-ui.html

## 🔧 配置说明

### 前端配置
```typescript
// vite.config.ts - 开发代理配置
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

### 后端配置
```yaml
# application.yml - 主要配置项
spring:
  datasource:
    url: ************************************
    username: root
    password: root
  data:
    redis:
      host: localhost
      port: 6379
      database: 5

jpzz:
  jwt:
    secret-key: happy-music-secret-key
    expiration: 864000000
  minio:
    endpoint: http://localhost:9000
    access-key: admin
    secret-key: admin1234
```

## 🎨 界面预览

### 登录页面
- 简洁的登录界面
- JWT Token认证
- 记住登录状态

### 仪表板
- 数据概览统计
- 图表数据可视化
- 快捷操作入口
- 实时数据更新

### 管理页面
- 统一的CRUD操作界面
- 高级搜索和筛选
- 分页和排序功能
- 批量操作支持

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目维护者**: YJP
- **邮箱**: [<EMAIL>]
- **项目地址**: [GitHub Repository URL]

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和开源社区的支持！

---

📖 **更多详细信息请查看**: [项目架构分析报告.md](./项目架构分析报告.md)

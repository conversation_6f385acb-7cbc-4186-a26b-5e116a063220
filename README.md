# 🎵 Happy Music Client - 音乐客户端

## 📖 项目概述

Happy Music Client 是 Happy Music 音乐管理系统的客户端应用，为用户提供现代化的音乐播放和管理体验。项目采用与后台管理系统一致的技术栈，确保代码风格和用户体验的统一性。

## 🏗️ 技术栈

- **框架**: Vue 3 + TypeScript + Vite
- **UI库**: Element Plus + Tailwind CSS
- **状态管理**: Pinia + 持久化插件
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **图标**: @iconify/vue
- **动画**: @vueuse/motion

## ✨ 核心功能

### 🎯 已规划功能

- 🎵 **音乐播放器**: 播放/暂停/上一首/下一首/进度控制
- 🎶 **音乐库浏览**: 按歌手/专辑/歌单分类浏览
- 🔍 **搜索功能**: 歌曲/歌手/歌单全局搜索
- 📱 **个人歌单**: 创建和管理个人歌单
- ❤️ **收藏功能**: 收藏喜欢的歌曲和歌单
- 👤 **用户系统**: 登录/注册/个人中心
- 🎨 **主题切换**: 多种主题风格切换
- 📱 **响应式设计**: 支持桌面端和移动端

### 🚧 开发进度

- ✅ **项目基础搭建**: 完成
- 🔄 **核心布局开发**: 进行中
- ⏳ **音乐播放器核心**: 待开发
- ⏳ **音乐库功能**: 待开发
- ⏳ **用户功能**: 待开发
- ⏳ **搜索和优化**: 待开发

## 📁 项目结构

```
happy-music-client/
├── public/                    # 静态资源
├── src/
│   ├── api/                  # API接口层
│   │   ├── auth/            # 认证相关接口
│   │   ├── music/           # 音乐相关接口
│   │   └── user/            # 用户相关接口
│   ├── components/          # 公共组件
│   │   ├── Player/          # 播放器组件
│   │   ├── MusicCard/       # 音乐卡片
│   │   └── SearchBox/       # 搜索框
│   ├── layout/              # 布局组件
│   │   ├── ClientLayout.vue # 主布局
│   │   ├── Header/          # 顶部导航
│   │   ├── Sidebar/         # 侧边栏
│   │   └── PlayerBar/       # 底部播放器
│   ├── stores/              # 状态管理
│   │   ├── user.ts          # 用户状态
│   │   ├── player.ts        # 播放器状态
│   │   └── music.ts         # 音乐数据
│   ├── views/               # 页面组件
│   │   ├── Home/            # 首页（含轮播图预留）
│   │   ├── Library/         # 音乐库
│   │   ├── Search/          # 搜索页面
│   │   ├── Playlist/        # 歌单页面
│   │   ├── Profile/         # 个人中心
│   │   ├── Login/           # 登录页面
│   │   └── Register/        # 注册页面
│   ├── utils/               # 工具函数
│   │   ├── request.ts       # HTTP请求封装
│   │   ├── audio.ts         # 音频处理工具
│   │   └── format.ts        # 格式化工具
│   ├── types/               # TypeScript类型
│   │   ├── api.ts           # API相关类型
│   │   ├── user.ts          # 用户相关类型
│   │   └── music.ts         # 音乐相关类型
│   ├── styles/              # 样式文件
│   │   └── index.scss       # 主样式文件
│   ├── router/              # 路由配置
│   │   └── index.ts         # 路由定义
│   ├── App.vue              # 根组件
│   ├── main.ts              # 应用入口
│   └── env.d.ts             # 环境变量类型
├── package.json             # 项目依赖
├── vite.config.ts           # Vite配置
├── tailwind.config.js       # Tailwind配置
├── tsconfig.json            # TypeScript配置
└── README.md                # 项目说明
```

## 🚀 快速开始

### 环境要求

- **Node.js**: 18+
- **npm**: 8+

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 🎨 设计规范

### 色彩方案
- **主色调**: 紫蓝渐变 (#667eea → #764ba2)
- **辅助色**: 粉紫渐变 (#f093fb → #f5576c)
- **强调色**: 蓝青渐变 (#4facfe → #00f2fe)

### 组件风格
- **玻璃态卡片**: 半透明背景 + 模糊效果
- **渐变按钮**: 主题色渐变 + 悬停效果
- **音乐卡片**: 白色背景 + 悬停动画
- **播放器控制栏**: 毛玻璃效果 + 阴影

### 响应式断点
- **移动端**: < 768px
- **平板端**: 768px - 1024px
- **桌面端**: > 1024px

## 🔧 开发规范

### 代码风格
- 使用 TypeScript 进行类型安全开发
- 遵循 Vue 3 Composition API 规范
- 使用 ESLint + Prettier 进行代码格式化

### 组件命名
- 组件文件使用 PascalCase
- 组件名称使用 kebab-case
- 事件名称使用 camelCase

### 状态管理
- 使用 Pinia 进行状态管理
- 重要状态启用持久化存储
- 按功能模块划分 Store

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: [<EMAIL>]

---

**Happy Music Client** - 享受美妙的音乐时光 🎵

package com.jpzz.service;

import com.jpzz.pojo.dto.LoginDTO;
import com.jpzz.pojo.vo.UserLoginVO;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;

@SpringBootTest
public class TokenTest {

    @Resource
    private StringRedisTemplate template;
    @Resource
    private AuthService authService;
    @Test
    public void testGenerateToken() {
        template.opsForValue().set("token", "123456");
        System.out.println(template.opsForValue().get("token"));
    }
    @Test
    public void testVerifyToken() {
        try {
            LoginDTO loginDTO = new LoginDTO();
            loginDTO.setUsername("admin");
            loginDTO.setPassword("123456");

            UserLoginVO result = authService.login(loginDTO);
            System.out.println("登录成功: " + result);

        } catch (Exception e) {
            System.out.println("登录失败: " + e.getMessage());
            System.out.println("请先在数据库中创建测试用户");
        }
    }
}

package com.jpzz.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * 歌手数据传输对象
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "歌手数据传输对象")
public class SingerDTO {

    @Schema(description = "歌手ID（修改时必填）", example = "1")
    private Long singerId;

    @NotBlank(message = "歌手名称不能为空")
    @Size(max = 50, message = "歌手名称长度不能超过50个字符")
    @Schema(description = "歌手名称", example = "周杰伦")
    private String singerName;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @NotBlank(message = "歌手类型不能为空")
    @Schema(description = "歌手类型", example = "男歌手", allowableValues = {"男歌手", "女歌手", "组合"})
    private String type;

    @Schema(description = "出生日期", example = "1979-01-18")
    private LocalDate birth;

    @Schema(description = "地区", example = "中国台湾")
    private String location;

    @Size(max = 500, message = "简介长度不能超过500个字符")
    @Schema(description = "个人简介", example = "华语流行音乐歌手、词曲创作人")
    private String introduction;

    @Schema(description = "歌曲数量", example = "150")
    private Integer songsCount;

    @Schema(description = "粉丝数量", example = "50000000")
    private Long fansCount;
}
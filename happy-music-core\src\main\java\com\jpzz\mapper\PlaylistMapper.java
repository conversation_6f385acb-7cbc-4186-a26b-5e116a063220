package com.jpzz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jpzz.pojo.dto.PlaylistQueryDTO;
import com.jpzz.pojo.entity.Playlist;
import com.jpzz.pojo.vo.PlaylistVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 歌单Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface PlaylistMapper extends BaseMapper<Playlist> {




}
package com.jpzz.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jpzz.mapper.FeedbackMapper;
import com.jpzz.mapper.PlaylistMapper;
import com.jpzz.mapper.SingerMapper;
import com.jpzz.mapper.SongMapper;
import com.jpzz.mapper.UserMapper;
import com.jpzz.pojo.vo.ContentStatsVO;
import com.jpzz.pojo.vo.DashboardOverviewVO;
import com.jpzz.pojo.vo.FeedbackStatsVO;
import com.jpzz.pojo.vo.UserTrendVO;
import com.jpzz.pojo.entity.*;
import com.jpzz.service.DashboardService;
import com.jpzz.utils.DateTimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仪表板服务实现类
 * <AUTHOR>
 */
@Service
@Slf4j
public class DashboardServiceImpl implements DashboardService {

    @Resource
    private UserMapper userMapper;
    
    @Resource
    private SingerMapper singerMapper;
    
    @Resource
    private SongMapper songMapper;
    
    @Resource
    private PlaylistMapper playlistMapper;
    
    @Resource
    private FeedbackMapper feedbackMapper;

    @Override
    public DashboardOverviewVO getOverview() {
        log.info("获取仪表板概览数据");

        DashboardOverviewVO overview = new DashboardOverviewVO();
        
        // 用户统计
        DashboardOverviewVO.UserStatsVO userStats = new DashboardOverviewVO.UserStatsVO();
        userStats.setTotalUsers(getTotalUsers());
        userStats.setTodayNewUsers(getTodayNewUsers());
        userStats.setActiveUsers(getActiveUsers());
        overview.setUserStats(userStats);
        
        // 内容统计
        DashboardOverviewVO.ContentStatsVO contentStats = new DashboardOverviewVO.ContentStatsVO();
        contentStats.setTotalSingers(getTotalSingers());
        contentStats.setTotalSongs(getTotalSongs());
        contentStats.setTotalPlaylists(getTotalPlaylists());
        overview.setContentStats(contentStats);
        
        // 互动统计
        DashboardOverviewVO.InteractionStatsVO interactionStats = new DashboardOverviewVO.InteractionStatsVO();
        interactionStats.setTotalPlays(getTotalPlays());
        interactionStats.setTotalLikes(getTotalLikes());
        interactionStats.setTotalCollections(getTotalCollections());
        overview.setInteractionStats(interactionStats);
        
        // 系统统计
        DashboardOverviewVO.SystemStatsVO systemStats = new DashboardOverviewVO.SystemStatsVO();
        systemStats.setTotalFeedbacks(getTotalFeedbacks());
        systemStats.setPendingFeedbacks(getPendingFeedbacks());
        systemStats.setSystemStatus("normal");
        overview.setSystemStats(systemStats);
        
        return overview;
    }

    @Override
    public List<UserTrendVO> getUserTrend(Integer days) {
        log.info("获取用户增长趋势，天数：{}", days);
        
        List<UserTrendVO> result = new ArrayList<>();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(days - 1);

        // 生成日期范围
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            UserTrendVO trendDTO = new UserTrendVO();
            trendDTO.setDate(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            
            // 查询当天新增用户数
            LocalDateTime dayStart = date.atStartOfDay();
            LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
            
            Long newUsers = userMapper.selectCount(
                new LambdaQueryWrapper<User>()
                    .ge(User::getCreateTime, dayStart)
                    .lt(User::getCreateTime, dayEnd)
            );
            
            trendDTO.setNewUsers(newUsers);
            // 暂时设为0，后续可以根据登录日志表计算
            trendDTO.setActiveUsers(0L);
            
            result.add(trendDTO);
        }
        
        return result;
    }

    @Override
    public List<FeedbackStatsVO> getFeedbackStats() {
        log.info("获取反馈统计");
        
        // 查询所有反馈
        List<Feedback> feedbacks = feedbackMapper.selectList(
            new LambdaQueryWrapper<Feedback>()
        );
        
        // 按类型分组统计
        Map<String, Long> typeCountMap = feedbacks.stream()
            .collect(Collectors.groupingBy(
                Feedback::getType,
                Collectors.counting()
            ));
        
        // 转换为DTO并计算百分比
        List<FeedbackStatsVO> result = new ArrayList<>();
        Long total = (long) feedbacks.size();
        
        typeCountMap.forEach((type, count) -> {
            FeedbackStatsVO statsDTO = new FeedbackStatsVO();
            statsDTO.setType(type);
            statsDTO.setCount(count);
            
            if (total > 0) {
                double percentage = (double) count / total * 100;
                statsDTO.setPercentage(Math.round(percentage * 100.0) / 100.0);
            } else {
                statsDTO.setPercentage(0.0);
            }
            
            result.add(statsDTO);
        });
        
        // 按数量降序排序
        result.sort((a, b) -> b.getCount().compareTo(a.getCount()));
        
        return result;
    }

    @Override
    public List<ContentStatsVO> getContentStats(Integer months) {
        log.info("获取内容统计，月数：{}", months);
        
        List<ContentStatsVO> result = new ArrayList<>();
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusMonths(months - 1).withDayOfMonth(1);
        
        // 生成月份范围
        for (LocalDate date = startDate; !date.isAfter(endDate.withDayOfMonth(1)); date = date.plusMonths(1)) {
            ContentStatsVO statsDTO = new ContentStatsVO();
            statsDTO.setMonth(date.format(DateTimeFormatter.ofPattern("yyyy-MM")));
            
            // 查询当月歌曲上传数
            LocalDateTime monthStart = date.atStartOfDay();
            LocalDateTime monthEnd = date.plusMonths(1).atStartOfDay();
            
            Long songUploads = songMapper.selectCount(
                new LambdaQueryWrapper<Song>()
                    .ge(Song::getCreateTime, monthStart)
                    .lt(Song::getCreateTime, monthEnd)
            );
            
            // 查询当月歌手注册数
            Long singerRegistrations = singerMapper.selectCount(
                new LambdaQueryWrapper<Singer>()
                    .ge(Singer::getCreateTime, monthStart)
                    .lt(Singer::getCreateTime, monthEnd)
            );
            
            statsDTO.setSongUploads(songUploads);
            statsDTO.setSingerRegistrations(singerRegistrations);
            
            result.add(statsDTO);
        }
        
        return result;
    }
    

    private Long getTotalUsers() {
        return userMapper.selectCount(new LambdaQueryWrapper<User>());
    }
    
    private Long getTodayNewUsers() {
        LocalDateTime todayStart = LocalDate.now().atStartOfDay();
        LocalDateTime todayEnd = todayStart.plusDays(1);
        
        return userMapper.selectCount(
            new LambdaQueryWrapper<User>()
                .ge(User::getCreateTime, todayStart)
                .lt(User::getCreateTime, todayEnd)
        );
    }
    
    private Long getActiveUsers() {
        // 暂时返回总用户数的80%作为活跃用户（实际项目中应该根据登录日志计算）
        Long totalUsers = getTotalUsers();
        return (long) (totalUsers * 0.8);
    }
    
    private Long getTotalSingers() {
        return singerMapper.selectCount(new LambdaQueryWrapper<Singer>());
    }
    
    private Long getTotalSongs() {
        return songMapper.selectCount(new LambdaQueryWrapper<Song>());
    }
    
    private Long getTotalPlaylists() {
        return playlistMapper.selectCount(new LambdaQueryWrapper<Playlist>());
    }
    
    private Long getTotalPlays() {
        // 暂时返回歌曲数量的1000倍作为播放量（实际项目中应该有播放记录表）
        Long totalSongs = getTotalSongs();
        return totalSongs * 1000;
    }
    
    private Long getTotalLikes() {
        // 查询所有歌曲的点赞数总和
        List<Song> songs = songMapper.selectList(
            new LambdaQueryWrapper<Song>()
                .select(Song::getLikeCount)
        );
        
        return songs.stream()
            .mapToLong(song -> song.getLikeCount() != null ? song.getLikeCount() : 0)
            .sum();
    }
    
    private Long getTotalCollections() {
        // 暂时返回歌单数量的100倍作为收藏数（实际项目中应该有收藏记录表）
        Long totalPlaylists = getTotalPlaylists();
        return totalPlaylists * 100;
    }
    
    private Long getTotalFeedbacks() {
        return feedbackMapper.selectCount(new LambdaQueryWrapper<Feedback>());
    }
    
    private Long getPendingFeedbacks() {
        return feedbackMapper.selectCount(
            new LambdaQueryWrapper<Feedback>()
                .eq(Feedback::getStatus, "PENDING")
        );
    }
}

import request from '@/utils/request'
import type { UserInfo, LoginForm, RegisterForm } from '@/types/user'

/**
 * 登录接口
 * @param username 用户名
 * @param password 密码
 * @returns Promise<{token: string}>
 */
export async function login(username: string, password: string) {
  try {
    return await request.post<{ token: string }>('/auth/login', {
      username,
      password
    })
  } catch (error) {
    return Promise.reject(error)
  }
}

/**
 * 注册接口
 * @param registerForm 注册表单数据
 * @returns Promise<{token: string}>
 */
export async function register(registerForm: RegisterForm) {
  try {
    return await request.post<{ token: string }>('/auth/register', registerForm)
  } catch (error) {
    return Promise.reject(error)
  }
}

/**
 * 获取当前用户信息
 * @returns Promise<UserInfo>
 */
export async function getUserInfo() {
  try {
    return await request.get<UserInfo>('/auth/userinfo')
  } catch (error) {
    return Promise.reject(error)
  }
}

/**
 * 登出接口
 * @returns Promise<void>
 */
export async function logout() {
  try {
    return await request.post<void>('/auth/logout')
  } catch (error) {
    return Promise.reject(error)
  }
}

/**
 * 刷新token
 * @returns Promise<{token: string}>
 */
export async function refreshToken() {
  try {
    return await request.post<{ token: string }>('/auth/refresh')
  } catch (error) {
    return Promise.reject(error)
  }
}

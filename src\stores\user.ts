import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { UserInfo, LoginForm } from '@/types/user'
import { login, getUserInfo, logout } from '@/api/auth'
import { ElMessage } from 'element-plus'
import router from '@/router'

export const useUserStore = defineStore('user', () => {
  // 状态
  const userInfo = ref<UserInfo | null>(null)
  const token = ref<string>('')

  // 登录
  const loginAction = async (loginForm: LoginForm) => {
    try {
      const response = await login(loginForm.username, loginForm.password)
      token.value = response.token
      localStorage.setItem('token', response.token)
      
      // 获取用户信息
      await getUserInfoAction()
      
      ElMessage.success('登录成功')
      
      // 跳转到首页或重定向页面
      const redirect = router.currentRoute.value.query.redirect as string
      router.push(redirect || '/')
      
      return response
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  // 获取用户信息
  const getUserInfoAction = async () => {
    try {
      const response = await getUserInfo()
      userInfo.value = response
      return response
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }

  // 登出
  const logoutAction = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      // 清除本地数据
      userInfo.value = null
      token.value = ''
      localStorage.removeItem('token')
      
      ElMessage.success('已退出登录')
      router.push('/login')
    }
  }

  // 清除用户数据（用于token失效等情况）
  const clearUserData = () => {
    userInfo.value = null
    token.value = ''
    localStorage.removeItem('token')
  }

  return {
    // 状态
    userInfo,
    token,
    
    // 方法
    login: loginAction,
    getUserInfo: getUserInfoAction,
    logout: logoutAction,
    clearUserData
  }
}, {
  persist: {
    key: 'happy-music-user',
    storage: localStorage,
    paths: ['userInfo'] // 只持久化用户信息，token通过localStorage单独管理
  }
})

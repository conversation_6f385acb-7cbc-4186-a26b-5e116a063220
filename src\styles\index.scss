@tailwind base;
@tailwind components;
@tailwind utilities;

/* 🎨 Happy Music Client 主题变量 */
:root {
  /* 主色调 - 音乐主题的紫蓝渐变 */
  --primary-color: #667eea;
  --primary-light: #764ba2;
  --primary-dark: #5a67d8;

  /* 辅助色彩 */
  --secondary-color: #f093fb;
  --accent-color: #4facfe;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 背景渐变 */
  --bg-gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  --bg-gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, #f5576c 100%);
  --bg-gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, #00f2fe 100%);

  /* 玻璃态效果 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* 圆角 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* 过渡动画 */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 🎵 全局基础样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--gray-50);
  color: var(--gray-900);
  line-height: 1.6;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 🎶 音乐主题组件样式 */
@layer components {
  /* 玻璃态卡片 */
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal) cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.5);
  }

  /* 渐变按钮 */
  .gradient-button {
    background: var(--bg-gradient-primary);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
  }

  .gradient-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .gradient-button:active {
    transform: translateY(0);
  }

  /* 音乐卡片 */
  .music-card {
    @apply bg-white rounded-xl p-4 shadow-md hover:shadow-lg transition-all duration-300;
    border: 1px solid var(--gray-200);
  }

  .music-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary-color);
  }

  /* 播放器控制栏 */
  .player-bar {
    @apply bg-white border-t border-gray-200 px-4 py-3;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
  }
}

/* 🎨 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: var(--radius-md);
  transition: background var(--transition-normal);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* 🎵 音频进度条样式 */
.audio-progress {
  appearance: none;
  width: 100%;
  height: 4px;
  border-radius: 2px;
  background: var(--gray-200);
  outline: none;
  transition: all var(--transition-normal);
}

.audio-progress::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.audio-progress::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: var(--shadow-lg);
}

/* 🎶 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* 🎵 响应式设计 */
@media (max-width: 768px) {
  .music-card {
    @apply p-3;
  }
  
  .player-bar {
    @apply px-3 py-2;
  }
}

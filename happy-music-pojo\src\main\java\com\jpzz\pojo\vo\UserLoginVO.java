package com.jpzz.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 登录响应VO
 * 用于返回登录成功后的用户信息和令牌
 * <AUTHOR>
 */
@Data
@Builder
@Schema(description = "登录响应结果")
public class UserLoginVO {

    /**
     * JWT令牌
     */
    @Schema(description = "JWT令牌", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String token;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID", example = "1")
    private Long userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名", example = "admin")
    private String username;

    /**
     * 头像URL
     */
    @Schema(description = "头像URL", example = "http://example.com/avatar.jpg")
    private String avatar;

    /**
     * 用户角色
     */
    @Schema(description = "用户角色", example = "ADMIN")
    private String role;
}
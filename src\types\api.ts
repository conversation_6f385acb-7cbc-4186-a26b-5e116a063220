// API 响应通用类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页查询参数
export interface PageParams {
  page: number
  size: number
}

// 分页响应数据
export interface PageResult<T> {
  total: number
  list: T[]
  page: number
  size: number
}

// 通用查询参数
export interface QueryParams extends PageParams {
  keyword?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

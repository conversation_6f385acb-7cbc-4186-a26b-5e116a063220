// 用户信息类型
export interface UserInfo {
  id: number
  username: string
  email?: string
  avatar?: string
  nickname?: string
  phone?: string
  gender?: 0 | 1 | 2 // 0: 未知, 1: 男, 2: 女
  birthday?: string
  signature?: string
  status: 0 | 1 // 0: 禁用, 1: 启用
  createTime: string
  updateTime: string
}

// 登录表单类型
export interface LoginForm {
  username: string
  password: string
  remember?: boolean
}

// 注册表单类型
export interface RegisterForm {
  username: string
  password: string
  confirmPassword: string
  email?: string
  phone?: string
}

// 用户查询参数
export interface UserQueryParams {
  page: number
  size: number
  keyword?: string
  status?: 0 | 1
}

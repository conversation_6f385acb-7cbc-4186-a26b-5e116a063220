package com.jpzz.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.io.FileUtil;
import com.jpzz.pojo.dto.SingerDTO;
import com.jpzz.pojo.dto.SingerQueryDTO;
import com.jpzz.pojo.vo.SingerVO;
import com.jpzz.result.PageResult;
import com.jpzz.result.Result;
import com.jpzz.service.SingerService;
import com.jpzz.utils.FileValidationUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/singer")
@Tag(name = "歌手管理", description = "歌手相关接口")
public class SingerController {

    @Resource
    private SingerService singerService;

    @GetMapping("/list")
    @Operation(summary = "分页查询歌手", description = "查询歌手列表")
    public Result<PageResult<SingerVO>> pageSingerList(SingerQueryDTO singerQueryDTO) {
        PageResult<SingerVO> pageResult = singerService.pageSingerList(singerQueryDTO);
        log.info("查询歌手列表成功:{}", pageResult);
        return Result.success("查询成功", pageResult);
    }

    @GetMapping("/{singerId}")
    @Operation(summary = "查询歌手", description = "查询歌手信息")
    public Result<SingerVO> getSingerById(@Validated @PathVariable Long singerId) {
        SingerVO singerVO = singerService.getSingerById(singerId);
        log.info("查询歌手信息成功:{}", singerVO);
        return Result.success("查询成功", singerVO);
    }

    @PostMapping
    @Operation(summary = "添加歌手", description = "添加歌手")
    public Result<Long> addSinger(@Validated @RequestBody SingerDTO singerDTO) {
        Long singerId = singerService.addSinger(singerDTO);
        log.info("添加歌手成功:{}", singerDTO);
        return Result.success("添加成功", singerId);
    }

    @PutMapping
    @Operation(summary = "修改歌手", description = "修改歌手信息")
    public Result<Void> updateSinger(@Validated @RequestBody SingerDTO singerDTO) {
        singerService.updateSinger(singerDTO);
        log.info("更新歌手信息成功:{}", singerDTO);
        return Result.success("更新成功");
    }

    @DeleteMapping("/{singerId}")
    @Operation(summary = "删除歌手", description = "删除歌手信息")
    public Result<Void> deleteSinger(@Validated @PathVariable Long singerId) {
        singerService.deleteById(singerId);
        return Result.success("删除成功");
    }

    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除歌手", description = "批量删除多个歌手")
    public Result<Void> batchDeleteSingers(@Validated @RequestBody Integer[] singerIds) {
        List<Long> singerIdsList = Convert.toList(Long.class, singerIds);
        singerService.deleteByIds(singerIdsList);
        return Result.success("批量删除成功");
    }

    @PostMapping("/{singerId}/avatar")
    @Operation(summary = "上传歌手头像", description = "上传歌手头像")
    public Result<Map<String, String>> uploadAvatar(
           @Parameter(description = "歌手ID") @PathVariable Long singerId,
           @Parameter(description = "头像文件") @RequestParam("file") MultipartFile file
           ) {

        // 文件验证
        String validationError = FileValidationUtils.validateImageFile(file, 5);
        if (validationError != null) {
            return Result.error(validationError);
        }
        try {
            String avatarUrl = singerService.uploadAvatar(file, singerId);
            Map<String, String> result = new HashMap<>(10);
            result.put("avatarUrl", avatarUrl);
            return Result.success("头像上传成功", result);
        } catch (Exception e) {
            log.error("头像上传失败: {}", e.getMessage());
            return Result.error("头像上传失败: " + e.getMessage());
        }

    }
}

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { SongInfo, PlayerState } from '@/types/music'

export const usePlayerStore = defineStore('player', () => {
  // 状态
  const currentSong = ref<SongInfo | null>(null)
  const playlist = ref<SongInfo[]>([])
  const currentIndex = ref(0)
  const isPlaying = ref(false)
  const volume = ref(0.7)
  const currentTime = ref(0)
  const duration = ref(0)
  const playMode = ref<'order' | 'random' | 'repeat' | 'single'>('order')

  // 计算属性
  const hasCurrentSong = computed(() => currentSong.value !== null)
  const hasPlaylist = computed(() => playlist.value.length > 0)
  const canPlayPrevious = computed(() => hasPlaylist.value && currentIndex.value > 0)
  const canPlayNext = computed(() => hasPlaylist.value && currentIndex.value < playlist.value.length - 1)

  // 播放歌曲
  const playSong = (song: SongInfo, songList?: SongInfo[]) => {
    currentSong.value = song
    
    if (songList) {
      playlist.value = songList
      currentIndex.value = songList.findIndex(s => s.id === song.id)
    } else if (playlist.value.length === 0) {
      playlist.value = [song]
      currentIndex.value = 0
    } else {
      const existingIndex = playlist.value.findIndex(s => s.id === song.id)
      if (existingIndex !== -1) {
        currentIndex.value = existingIndex
      } else {
        playlist.value.push(song)
        currentIndex.value = playlist.value.length - 1
      }
    }
    
    isPlaying.value = true
  }

  // 播放/暂停
  const togglePlay = () => {
    isPlaying.value = !isPlaying.value
  }

  // 上一首
  const playPrevious = () => {
    if (!hasPlaylist.value) return
    
    if (playMode.value === 'random') {
      const randomIndex = Math.floor(Math.random() * playlist.value.length)
      currentIndex.value = randomIndex
    } else if (currentIndex.value > 0) {
      currentIndex.value--
    } else if (playMode.value === 'repeat') {
      currentIndex.value = playlist.value.length - 1
    }
    
    currentSong.value = playlist.value[currentIndex.value]
    isPlaying.value = true
  }

  // 下一首
  const playNext = () => {
    if (!hasPlaylist.value) return
    
    if (playMode.value === 'random') {
      const randomIndex = Math.floor(Math.random() * playlist.value.length)
      currentIndex.value = randomIndex
    } else if (currentIndex.value < playlist.value.length - 1) {
      currentIndex.value++
    } else if (playMode.value === 'repeat') {
      currentIndex.value = 0
    }
    
    currentSong.value = playlist.value[currentIndex.value]
    isPlaying.value = true
  }

  // 设置音量
  const setVolume = (newVolume: number) => {
    volume.value = Math.max(0, Math.min(1, newVolume))
  }

  // 设置播放进度
  const setCurrentTime = (time: number) => {
    currentTime.value = Math.max(0, Math.min(duration.value, time))
  }

  // 设置歌曲总时长
  const setDuration = (newDuration: number) => {
    duration.value = newDuration
  }

  // 切换播放模式
  const togglePlayMode = () => {
    const modes: Array<'order' | 'random' | 'repeat' | 'single'> = ['order', 'random', 'repeat', 'single']
    const currentModeIndex = modes.indexOf(playMode.value)
    playMode.value = modes[(currentModeIndex + 1) % modes.length]
  }

  // 添加歌曲到播放列表
  const addToPlaylist = (song: SongInfo) => {
    const existingIndex = playlist.value.findIndex(s => s.id === song.id)
    if (existingIndex === -1) {
      playlist.value.push(song)
    }
  }

  // 从播放列表移除歌曲
  const removeFromPlaylist = (songId: number) => {
    const index = playlist.value.findIndex(s => s.id === songId)
    if (index !== -1) {
      playlist.value.splice(index, 1)
      
      // 如果移除的是当前播放的歌曲
      if (index === currentIndex.value) {
        if (playlist.value.length === 0) {
          currentSong.value = null
          currentIndex.value = 0
          isPlaying.value = false
        } else {
          // 播放下一首，如果没有下一首则播放上一首
          if (currentIndex.value >= playlist.value.length) {
            currentIndex.value = playlist.value.length - 1
          }
          currentSong.value = playlist.value[currentIndex.value]
        }
      } else if (index < currentIndex.value) {
        // 如果移除的歌曲在当前歌曲之前，需要调整当前索引
        currentIndex.value--
      }
    }
  }

  // 清空播放列表
  const clearPlaylist = () => {
    playlist.value = []
    currentSong.value = null
    currentIndex.value = 0
    isPlaying.value = false
    currentTime.value = 0
    duration.value = 0
  }

  return {
    // 状态
    currentSong,
    playlist,
    currentIndex,
    isPlaying,
    volume,
    currentTime,
    duration,
    playMode,
    
    // 计算属性
    hasCurrentSong,
    hasPlaylist,
    canPlayPrevious,
    canPlayNext,
    
    // 方法
    playSong,
    togglePlay,
    playPrevious,
    playNext,
    setVolume,
    setCurrentTime,
    setDuration,
    togglePlayMode,
    addToPlaylist,
    removeFromPlaylist,
    clearPlaylist
  }
}, {
  persist: {
    key: 'happy-music-player',
    storage: localStorage,
    paths: ['playlist', 'currentIndex', 'volume', 'playMode'] // 持久化播放列表和设置
  }
})

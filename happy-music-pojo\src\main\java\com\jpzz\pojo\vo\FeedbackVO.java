package com.jpzz.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 反馈VO
 * <AUTHOR>
 */
@Data
@Schema(description = "反馈VO")
public class FeedbackVO {

    @Schema(description = "反馈ID", example = "1")
    private Long id;

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "张三")
    private String username;

    @Schema(description = "用户头像", example = "http://example.com/avatar.jpg")
    private String userAvatar;

    @Schema(description = "反馈内容", example = "登录功能有问题")
    private String content;

    @Schema(description = "反馈类型", example = "BUG")
    private String type;

    @Schema(description = "反馈类型描述", example = "Bug反馈")
    private String typeDesc;

    @Schema(description = "反馈状态", example = "PENDING")
    private String status;

    @Schema(description = "反馈状态描述", example = "待处理")
    private String statusDesc;

    @Schema(description = "管理员回复", example = "感谢您的反馈，我们会尽快处理")
    private String adminReply;

    @Schema(description = "回复时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime replyTime;

    @Schema(description = "创建时间", example = "2024-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
}
package com.jpzz.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 反馈更新DTO
 * 用于更新反馈状态和管理员回复
 * <AUTHOR>
 */
@Data
@Schema(description = "反馈更新DTO")
public class FeedbackUpdateDTO {

    /**
     * 反馈状态（可选）
     */
    @Schema(description = "反馈状态", example = "RESOLVED", allowableValues = {"PENDING", "PROCESSING", "RESOLVED", "CLOSED"})
    private String status;

    /**
     * 管理员回复（可选）
     */
    @Schema(description = "管理员回复", example = "感谢您的反馈，我们会尽快处理")
    private String reply;
}
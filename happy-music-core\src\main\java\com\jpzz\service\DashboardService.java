package com.jpzz.service;


import com.jpzz.pojo.vo.ContentStatsVO;
import com.jpzz.pojo.vo.DashboardOverviewVO;
import com.jpzz.pojo.vo.FeedbackStatsVO;
import com.jpzz.pojo.vo.UserTrendVO;

import java.util.List;

/**
 * 仪表板服务接口
 * <AUTHOR>
 */
public interface DashboardService  {

    /**
     * 获取仪表板概览数据
     *
     * @return 概览数据
     */
    DashboardOverviewVO getOverview();

    /**
     * 获取用户增长趋势
     * @param days 天数
     * @return 用户趋势数据
     */
    List<UserTrendVO> getUserTrend(Integer days);

    /**
     * 获取反馈统计
     * @return 反馈统计数据
     */
    List<FeedbackStatsVO> getFeedbackStats();

    /**
     * 获取内容统计
     * @param months 月数
     * @return 内容统计数据
     */
    List<ContentStatsVO> getContentStats(Integer months);
}
package com.jpzz.controller;


import com.jpzz.pojo.vo.ContentStatsVO;
import com.jpzz.pojo.vo.DashboardOverviewVO;
import com.jpzz.pojo.vo.FeedbackStatsVO;
import com.jpzz.pojo.vo.UserTrendVO;
import com.jpzz.result.Result;
import com.jpzz.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 仪表板控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/dashboard")
@Tag(name = "仪表板管理", description = "仪表板数据统计相关接口")
@Slf4j
public class DashboardController {

    @Resource
    private DashboardService dashboardService;

    @GetMapping("/overview")
    @Operation(summary = "获取仪表板概览数据")
    public Result<DashboardOverviewVO> getOverview() {
        log.info("获取仪表板概览数据");
        DashboardOverviewVO overview = dashboardService.getOverview();
        return Result.success(overview);
    }

    @GetMapping("/user-trend")
    @Operation(summary = "获取用户增长趋势")
    public Result<List<UserTrendVO>> getUserTrend(
            @Parameter(description = "天数，默认30天") @RequestParam(defaultValue = "30") Integer days) {
        log.info("获取用户增长趋势，天数：{}", days);
        List<UserTrendVO> userTrend = dashboardService.getUserTrend(days);
        return Result.success(userTrend);
    }

    @GetMapping("/feedback-stats")
    @Operation(summary = "获取反馈统计")
    public Result<List<FeedbackStatsVO>> getFeedbackStats() {
        log.info("获取反馈统计");
        List<FeedbackStatsVO> feedbackStats = dashboardService.getFeedbackStats();
        return Result.success("获取成功", feedbackStats);
    }

    @GetMapping("/content-stats")
    @Operation(summary = "获取内容统计")
    public Result<List<ContentStatsVO>> getContentStats(
            @Parameter(description = "月数，默认12个月") @RequestParam(defaultValue = "12") Integer months) {
        log.info("获取内容统计，月数：{}", months);
        List<ContentStatsVO> contentStats = dashboardService.getContentStats(months);
        return Result.success("获取成功", contentStats);
    }
}

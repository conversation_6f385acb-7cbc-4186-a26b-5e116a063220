package com.jpzz.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 歌单歌曲操作DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "歌单歌曲操作DTO")
public class PlaylistSongDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @NotNull(message = "歌单ID不能为空")
    @Schema(description = "歌单ID")
    private Long playlistId;

    @NotEmpty(message = "歌曲ID列表不能为空")
    @Schema(description = "歌曲ID列表")
    private List<Long> songIds;
}
import { createRouter, createWebHistory } from 'vue-router'
import ClientLayout from '@/layout/ClientLayout.vue'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('@/views/Login/index.vue'),
      meta: {
        title: '登录',
        requiresAuth: false
      }
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('@/views/Register/index.vue'),
      meta: {
        title: '注册',
        requiresAuth: false
      }
    },
    {
      path: '/',
      component: ClientLayout,
      children: [
        {
          path: '',
          name: 'home',
          component: () => import('@/views/Home/index.vue'),
          meta: {
            title: '首页',
            requiresAuth: false
          }
        },
        {
          path: '/library',
          name: 'library',
          component: () => import('@/views/Library/index.vue'),
          meta: {
            title: '音乐库',
            requiresAuth: false
          }
        },
        {
          path: '/playlist',
          name: 'playlist',
          component: () => import('@/views/Playlist/index.vue'),
          meta: {
            title: '歌单',
            requiresAuth: true
          }
        },
        {
          path: '/search',
          name: 'search',
          component: () => import('@/views/Search/index.vue'),
          meta: {
            title: '搜索',
            requiresAuth: false
          }
        },
        {
          path: '/profile',
          name: 'profile',
          component: () => import('@/views/Profile/index.vue'),
          meta: {
            title: '个人中心',
            requiresAuth: true
          }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  document.title = `${to.meta.title as string || '默认标题'} - Happy Music`

  const userStore = useUserStore()
  const hasToken = localStorage.getItem('token')

  // 如果路由需要身份验证
  if (to.meta.requiresAuth) {
    // 如果有token
    if (hasToken) {
      // 确保有用户信息，如果没有则重新获取
      if (!userStore.userInfo) {
        try {
          await userStore.getUserInfo()
          next()
        } catch (error) {
          console.log(error)
          // 获取用户信息失败，可能是token失效
          userStore.logout()
          ElMessage.error('登录状态已过期，请重新登录')
          next(`/login?redirect=${to.path}`)
        }
      } else {
        next()
      }
    } else {
      // 无token，跳转到登录页面
      ElMessage.warning('请先登录')
      next(`/login?redirect=${to.path}`)
    }
  } else {
    // 不需要身份验证的路由
    if ((to.path === '/login' || to.path === '/register') && hasToken) {
      // 如果已登录，访问登录/注册页会重定向到首页
      next('/')
    } else {
      next()
    }
  }
})

export default router

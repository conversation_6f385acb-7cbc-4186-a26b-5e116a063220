package com.jpzz.pojo.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 反馈提交DTO
 * 用于用户提交反馈
 * <AUTHOR>
 */
@Data
@Schema(description = "反馈提交DTO")
public class FeedbackDTO {

    /**
     * 反馈内容
     */
    @NotBlank(message = "反馈内容不能为空")
    @Size(max = 1000, message = "反馈内容长度不能超过1000个字符")
    @Schema(description = "反馈内容", example = "登录功能有问题，无法正常登录")
    private String content;

    /**
     * 反馈类型
     */
    @NotBlank(message = "反馈类型不能为空")
    @Schema(description = "反馈类型", example = "BUG", allowableValues = {"BUG", "FEATURE", "COMPLAINT", "SUGGESTION", "OTHER"})
    private String type;
}
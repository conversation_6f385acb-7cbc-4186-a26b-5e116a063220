package com.jpzz.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 仪表板概览数据DTO
 * <AUTHOR>
 */
@Data
@Schema(description = "仪表板概览数据")
public class DashboardOverviewVO {

    @Schema(description = "用户统计")
    private UserStatsVO userStats;

    @Schema(description = "内容统计")
    private ContentStatsVO contentStats;

    @Schema(description = "互动统计")
    private InteractionStatsVO interactionStats;

    @Schema(description = "系统统计")
    private SystemStatsVO systemStats;

    @Data
    @Schema(description = "用户统计")
    public static class UserStatsVO {
        @Schema(description = "总用户数")
        private Long totalUsers;

        @Schema(description = "今日新增用户")
        private Long todayNewUsers;

        @Schema(description = "活跃用户数")
        private Long activeUsers;
    }

    @Data
    @Schema(description = "内容统计")
    public static class ContentStatsVO {
        @Schema(description = "歌手总数")
        private Long totalSingers;

        @Schema(description = "歌曲总数")
        private Long totalSongs;

        @Schema(description = "歌单总数")
        private Long totalPlaylists;
    }

    @Data
    @Schema(description = "互动统计")
    public static class InteractionStatsVO {
        @Schema(description = "总播放量")
        private Long totalPlays;

        @Schema(description = "总点赞数")
        private Long totalLikes;

        @Schema(description = "总收藏数")
        private Long totalCollections;
    }

    @Data
    @Schema(description = "系统统计")
    public static class SystemStatsVO {
        @Schema(description = "反馈总数")
        private Long totalFeedbacks;

        @Schema(description = "待处理反馈数")
        private Long pendingFeedbacks;

        @Schema(description = "系统状态")
        private String systemStatus;
    }
}
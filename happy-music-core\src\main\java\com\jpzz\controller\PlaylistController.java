package com.jpzz.controller;

import cn.hutool.core.convert.Convert;
import com.jpzz.pojo.dto.PlaylistDTO;
import com.jpzz.pojo.dto.PlaylistQueryDTO;
import com.jpzz.pojo.dto.PlaylistSongDTO;
import com.jpzz.pojo.dto.PlaylistSongQueryDTO;
import com.jpzz.pojo.vo.PlaylistVO;
import com.jpzz.pojo.vo.SongVO;
import com.jpzz.result.PageResult;
import com.jpzz.result.Result;
import com.jpzz.service.PlaylistService;
import com.jpzz.utils.FileValidationUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 歌单管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/playlist")
@Slf4j
@Tag(name = "歌单管理", description = "歌单相关接口")
public class PlaylistController {

    @Resource
    private PlaylistService playlistService;

    /**
     * 分页查询歌单列表
     */
    @GetMapping("/list")
    @Operation(summary = "分页查询歌单列表")
    public Result<PageResult<PlaylistVO>> getPlaylistList(PlaylistQueryDTO playlistQueryDTO) {
        log.info("分页查询歌单列表，查询条件：{}", playlistQueryDTO);
        PageResult<PlaylistVO> pageResult = playlistService.getPlaylistPage(playlistQueryDTO);
        return Result.success("查询成功", pageResult);
    }

    /**
     * 根据ID查询歌单详情
     */
    @GetMapping("/{playlistId}")
    @Operation(summary = "根据ID查询歌单详情")
    public Result<PlaylistVO> getPlaylistById(@Parameter(description = "歌单ID") @PathVariable Long playlistId) {
        log.info("根据ID查询歌单详情，歌单ID：{}", playlistId);
        PlaylistVO playlistVO = playlistService.getPlaylistById(playlistId);
        return Result.success("查询成功", playlistVO);
    }

    /**
     * 添加歌单
     */
    @PostMapping
    @Operation(summary = "添加歌单")
    public Result<Long> addPlaylist(@Valid @RequestBody PlaylistDTO playlistDTO) {
        log.info("添加歌单，歌单信息：{}", playlistDTO);
        Long playlistId = playlistService.addPlaylist(playlistDTO);
        return Result.success("添加成功", playlistId);
    }

    /**
     * 更新歌单
     */
    @PutMapping
    @Operation(summary = "更新歌单")
    public Result<Void> updatePlaylist(@Valid @RequestBody PlaylistDTO playlistDTO) {
        log.info("更新歌单，歌单信息：{}", playlistDTO);
        playlistService.updatePlaylist(playlistDTO);
        return Result.success("更新成功");
    }

    /**
     * 删除歌单
     */
    @DeleteMapping("/{playlistId}")
    @Operation(summary = "删除歌单")
    public Result<Void> deletePlaylistById(@Parameter(description = "歌单ID") @PathVariable Long playlistId) {
        log.info("删除歌单，歌单ID：{}", playlistId);
        playlistService.deletePlaylistById(playlistId);
        return Result.success("删除成功");
    }

    /**
     * 批量删除歌单
     */
    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除歌单")
    public Result<Void> batchDeletePlaylists(@Validated @RequestBody Integer[] playlistIds) {
        log.info("批量删除歌单，歌单ID列表：{}", (Object) playlistIds);
        List<Long> playlistList = Convert.toList(Long.class, playlistIds);
        playlistService.deletePlaylistByIds(playlistList);
        return Result.success("批量删除成功");
    }

    /**
     * 上传歌单封面
     */
    @PostMapping("/{playlistId}/cover")
    @Operation(summary = "上传歌单封面")
    public Result<Map<String, String>> uploadCover(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            @Parameter(description = "封面文件") @RequestParam("file") MultipartFile file) {

        log.info("上传歌单封面，歌单ID：{}", playlistId);
        // 文件验证
        String validationError = FileValidationUtils.validateImageFile(file, 5);
        if (validationError != null) {
            return Result.error(validationError);
        }
        try {
            String coverUrl = playlistService.uploadCover(file, playlistId);
            Map<String, String> result = new HashMap<>(10);
            result.put("coverUrl", coverUrl);
            return Result.success("头像上传成功", result);
        } catch (Exception e) {
            log.error("头像上传失败: {}", e.getMessage());
            return Result.error("头像上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取歌单中的歌曲列表
     */
    @GetMapping("/{playlistId}/songs")
    @Operation(summary = "获取歌单中的歌曲列表")
    public Result<PageResult<SongVO>> getPlaylistSongs(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            PlaylistSongQueryDTO queryDTO) {
        log.info("获取歌单中的歌曲列表，歌单ID：{}，查询条件：{}", playlistId, queryDTO);
        queryDTO.setPlaylistId(playlistId);
        PageResult<SongVO> pageResult = playlistService.getPlaylistSongs(queryDTO);
        return Result.success("查询成功", pageResult);
    }

    /**
     * 向歌单添加歌曲
     */
    @PostMapping("/{playlistId}/songs")
    @Operation(summary = "向歌单添加歌曲")
    public Result<Void> addSongsToPlaylist(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            @Validated @RequestBody Integer[] songIds) {
        log.info("向歌单添加歌曲，歌单ID：{}，歌曲信息：{}", playlistId, songIds);
        List<Long> songIdList = Convert.toList(Long.class, songIds);
        playlistService.addSongsToPlaylist(playlistId, songIdList);
        return Result.success("添加成功");
    }

    /**
     * 从歌单移除单首歌曲
     */
    @DeleteMapping("/{playlistId}/songs/{songId}")
    @Operation(summary = "从歌单移除单首歌曲")
    public Result<Void> removeSongFromPlaylist(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            @Parameter(description = "歌曲ID") @PathVariable Long songId) {
        log.info("从歌单移除歌曲，歌单ID：{}，歌曲ID：{}", playlistId, songId);
        PlaylistSongDTO playlistSongDTO = new PlaylistSongDTO();
        playlistSongDTO.setPlaylistId(playlistId);
        playlistSongDTO.setSongIds(List.of(songId));
        playlistService.removeSongsByPlaylist(playlistSongDTO);
        return Result.success("移出成功");
    }

    /**
     * 批量从歌单移除歌曲
     */
    @PostMapping("/{playlistId}/songs/batch-delete")
    @Operation(summary = "批量从歌单移除歌曲")
    public Result<Void> batchRemoveSongsFromPlaylist(
            @Parameter(description = "歌单ID") @PathVariable Long playlistId,
            @Valid @RequestBody Integer[] songIds) {
        log.info("批量从歌单移除歌曲，歌单ID：{}，歌曲信息：{}", playlistId, songIds);
        List<Long> songIdsList = Convert.toList(Long.class, songIds);
        PlaylistSongDTO playlistSongDTO = new PlaylistSongDTO();
        playlistSongDTO.setPlaylistId(playlistId);
        playlistSongDTO.setSongIds(songIdsList);
        playlistService.removeSongsByPlaylist(playlistSongDTO);
        return Result.success("批量移出成功");
    }
}
package com.jpzz.controller;

import cn.hutool.core.convert.Convert;
import com.jpzz.pojo.dto.SongDTO;
import com.jpzz.pojo.dto.SongQueryDTO;
import com.jpzz.pojo.vo.SongVO;
import com.jpzz.result.PageResult;
import com.jpzz.result.Result;
import com.jpzz.service.SongService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import com.jpzz.utils.FileValidationUtils;

/**
 * 歌曲管理控制器
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/song")
@Tag(name = "歌曲管理", description = "歌曲相关接口")
@Slf4j
public class SongController {

    @Resource
    private SongService songService;

    @GetMapping("/list")
    @Operation(summary = "分页查询歌曲列表", description = "支持按歌曲名称、歌手、风格、专辑等条件查询")
    public Result<PageResult<SongVO>> list(SongQueryDTO songQueryDTO) {
        log.info("分页查询歌曲列表，查询参数：{}", songQueryDTO);
        PageResult<SongVO> pageResult = songService.pageSongList(songQueryDTO);
        return Result.success("查询成功",pageResult);
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询歌曲详情")
    public Result<SongVO> getById(@Parameter(description = "歌曲ID") @PathVariable Long id) {
        log.info("根据ID查询歌曲详情，歌曲ID：{}", id);
        SongVO songVO = songService.getSongById(id);
        return Result.success("查询成功",songVO);
    }

    @PostMapping
    @Operation(summary = "新增歌曲")
    public Result<Long> add(@Validated @RequestBody SongDTO songDTO) {
        log.info("新增歌曲，歌曲信息：{}", songDTO);
        Long songId = songService.addSong(songDTO);
        return Result.success("新增成功!",songId);
    }

    @PutMapping
    @Operation(summary = "更新歌曲")
    public Result<Void> update(@Validated @RequestBody SongDTO songDTO) {
        log.info("更新歌曲，歌曲信息：{}", songDTO);
        songService.updateSong(songDTO);
        return Result.success("更新成功!");
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除歌曲")
    public Result<Void> deleteById(@Parameter(description = "歌曲ID") @PathVariable Long id) {
        log.info("删除歌曲，歌曲ID：{}", id);
        songService.deleteById(id);
        return Result.success("删除成功!");
    }

    @Operation(summary = "批量删除歌曲")
    @PostMapping("/batch-delete")
    public Result<Void> batchDeleteSongs(@RequestBody Integer[] songIds) {
        log.info("批量删除歌曲，歌曲ID列表：{}",(Object) songIds);
        List<Long> songIdsList = Convert.toList(Long.class, songIds);
        songService.deleteByIds(songIdsList);
        return Result.success("批量删除成功!");
    }

    @PostMapping("/{id}/cover")
    @Operation(summary = "上传歌曲封面")
    public Result<Map<String, String>> uploadCover(
            @Parameter(description = "歌曲ID") @PathVariable Long id,
            @Parameter(description = "封面文件") @RequestParam("file") MultipartFile file) {
        
        log.info("上传歌曲封面，歌曲ID：{}，文件名：{}", id, file.getOriginalFilename());
        
        // 文件验证
        String validationError = FileValidationUtils.validateImageFile(file, 5);
        if (validationError != null) {
            return Result.error(validationError);
        }
        try {
            String coverUrl = songService.uploadCover(file, id);
            Map<String, String> result = new HashMap<>();
            result.put("coverUrl", coverUrl);
            return Result.success("封面上传成功", result);
        } catch (Exception e) {
            log.error("封面上传失败: {}", e.getMessage());
            return Result.error("封面上传失败: " + e.getMessage());
        }
    }

    @PostMapping("/{id}/audio")
    @Operation(summary = "上传歌曲音频")
    public Result<Map<String, String>> uploadAudio(
            @Parameter(description = "歌曲ID") @PathVariable Long id,
            @Parameter(description = "音频文件") @RequestParam("file") MultipartFile file) {
        
        log.info("上传歌曲音频，歌曲ID：{}，文件名：{}", id, file.getOriginalFilename());
        // 文件验证
        String validationError = FileValidationUtils.validateAudioFile(file, 50);
        if (validationError != null) {
            return Result.error(validationError);
        }
        try {
            String audioUrl = songService.uploadAudio(file, id);
            Map<String, String> result = new HashMap<>();
            result.put("audioUrl", audioUrl);
            return Result.success("音频上传成功", result);
        } catch (Exception e) {
            log.error("音频上传失败: {}", e.getMessage());
            return Result.error("音频上传失败: " + e.getMessage());
        }
    }
}

